# about_ai
> AI의 능력과 정체성에 대한 질문 처리용 intent입니다.
> 46번째 줄부턴 지원하지 않는 수준이지만 벡터 공간 확보를 위해 추가합니다

## Examples
- 너 누구야?
- 넌 누구니?
- 넌 몇 살이야?
- 넌 무슨 일을 해?
- 너 뭐해?
- 너 뭐하는 놈이야?
- 너 뭐하는게 좋아?
- 너 뭐할 수 있어?
- 잘할 수 있어?
- 뭘 잘해?
- 너 뭐 못해?
- 너 몇 살이야?
- 너 언제 태어났어?
- 너 언제 만들어졌어?
- 넌 누구야?
- 당신은 누구세요?
- 너 이름이 뭐야?
- 너의 이름은 무엇인가요?
- 당신의 이름을 알려주세요.
- 넌 뭐하는 챗봇이야?
- 너의 역할이 뭐야?
- 넌 무슨 역할을 하나?
- 이 챗봇은 무엇을 하는 녀석인가요?
- 이 시스템은 어떤 기능을 제공하나요?
- 너는 어떤 목적으로 만들어졌어?
- 이 모델은 어떤 용도로 쓰이나요?
- GPT인가요?
- 너 GPT야?
- 너는 어떤 모델 기반이야?
- 너의 기반 모델은 무엇인가요?
- 너를 만든 회사가 어디야?
- 이 챗봇을 만든 사람은 누구죠?
- 어떻게 작동하나요?
- 무슨 기술을 사용하나요?
- 너는 AI야?
- 너는 인공지능이야?
- AI 어시스턴트 맞죠?
- 너는 어떤 AI이니?
- 내 질문을 어떻게 이해해?
- 어떻게 답변을 생성하나요?
- 실시간 정보를 조회할 수 있나요?
- 파라미터 개수가 정말 1조 개예요?  
- 최신 논문도 검색해 줘요?  
- 데이터 프라이버시 어떻게 보장돼요?  
- 코딩 미리보기 기능 있나요?  
- Llama랑 비교하면 뭐가 달라요?  
- GPT-4o랑 GPT-4 Turbo 차이 설명해 주세요  
- 답변 근거는 어디서 가져오나요?  
- 플러그인 쓰려면 유료인가요?  
- 지연 시간(latency) 얼마나 걸려요?  
- 멀티모달 입력도 처리 가능?  
- 토큰 한도 넘어가면 어떻게 돼?  
- 모델도 가끔 헛소리한다는데 왜 그래요?  
- 대화 내역 삭제할 수 있나요?  
- 나만의 프롬프트 프리셋 만들 수 있어?  
- 실시간 번역도 돼요?  
- 리눅스 터미널 에뮬레이션 가능?  
- 오프라인에서 사설 망으로 돌릴 수 있나요?  
- 반응형 디자인 코드도 짤 수 있어요?  
- 한글 맞춤법 검사 권한 있어요?  
- 롱텀 메모리는 어디까지 기억해요?  
- 출력 음성으로 바꿀 수 있나요?  
- API 호출비 얼마쯤?  
- 파인튜닝하면 성능 얼마나 향상돼요?  
- 윤리 필터는 어떤 기준?  
- 비전 기능 쓰면 이미지 저장하나요?  
- 자동화(task scheduling)까지 해 주나요?  
- GPU 인퍼런스랑 CPU 차이 체감돼요?  
- JSON 포맷 강제 출력 가능?  
- 네트워크 끊기면 대화 날아가나요?  
- 모델 이름 왜 오픈AI o3예요?
- 당신이 사용하는 언어 모델 버전이 궁금합니다
- GPT-4o랑 GPT-5의 차이점이 뭔가요?
- 토큰 한도 초과하면 어떻게 되나요?
- 프롬프트 길이 제한이 있나요?
- 응답 속도가 갑자기 느려질 때 이유가 뭔가요?
- 파인튜닝하면 비용이 많이 드나요?
- 개인 맞춤형 모델 저장이 가능한가요?
- 오늘 대화 내용을 내일도 기억하나요?
- 대화 기록 삭제 옵션이 어디 있죠?
- 실시간 검색 결과는 어떻게 가져오나요?
- hallucination 빈도를 줄이는 방법이 있나요?
- 모델이 편향을 가지지 않게 하는 방법은?
- 새로운 지식은 언제 학습되나요?
- API 키 노출되면 어떤 위험이 있나요?
- GPT를 오프라인에서 돌릴 수 있나요?
- 토큰화가 한글에도 최적화돼 있나요?
- 멀티모달 기능을 한국어로도 지원하나요?
- 이미지 업로드 시 개인정보 처리 방식이 궁금해요
- 음성 입력도 가능한가요?
- 코드 실행 샌드박스가 존재하나요?
- 대답이 반복되는 이유가 뭔가요?
- JSON 형식으로만 응답하게 강제할 수 있나요?
- 대화를 요약해 달라고 하면 정확도가 어떻죠?
- 플러그인 연동 시 승인을 받아야 하나요?
- 컨텍스트 리셋은 어떻게 하나요?
- 다른 LLM과 협업 호출이 가능합니까?
- GPT-4o의 파라미터 수는 정확히 얼마나 되나요?
- 출력 난독화를 막는 방법이 있나요?
- 모델이 개인정보를 학습하지 못하게 하는 메커니즘은?
- 사용자 피드백이 반영되는 주기가 궁금합니다
- 인터넷이 끊겨도 캐시 응답이 되나요?
- 대화에서 금칙어가 있나요?
- 비속어 필터링 레벨을 조정할 수 있나요?
- 응답 문체를 기본적으로 바꿀 수 있나요?
- 토큰 단가가 할인될 때가 있나요?
- 스레드 ID로 대화를 이어갈 수 있나요?
- 모델 업데이트 알림을 받을 수 있습니까?
- 프롬프트 엔지니어링 모범 사례 알려줘요
- 함수 호출 기능이 정확히 뭔가요?
- 장문의 문서를 나눠서 입력하면 연결되나요?
- 스트리밍 모드와 배치 모드 차이가 뭔가요?
- 출력에서 마크다운 표 지원하나요?
- 파이썬 코드를 실행시켜 줄 수 있나요?
- R 코드도 지원하나요?
- 로컬 GPU에서 추론할 때 최소 VRAM은?
- 미리 학습된 지식 컷오프 날짜가 언제죠?
- 토큰화 예시는 어디서 볼 수 있나요?
- SST 지표 같은 최신 데이터를 검색할 수 있나요?
- 정책 위반 시 어떤 에러 코드가 나오나요?
- 사용량 대시보드를 보는 법이 궁금합니다
- 실시간 주가 API 연결법을 알려줄 수 있나요?
- 서드파티 플러그인 설치 절차가 복잡한가요?
- JSON 스키마를 미리 전달하면 유용한가요?
- LLM이 PDF를 직접 읽을 수 있나요?
- 모델이 학습한 언어 분포가 궁금해요
- 하드웨어 NPU 가속 지원이 있나요?
- 디버깅 정보 출력이 가능합니까?
- API 호출 회수 제한이 있나요?
- 시스템 메시지 우선순위가 헷갈립니다
- 로그 레벨을 조정할 수 있나요?
- 자동 번역 정확도가 언어별로 다른가요?
- 오프라인 캐시 모델 업데이트는 어떻게?
- 웹 브라우징 기능이 임시 중단될 때 대처법?
